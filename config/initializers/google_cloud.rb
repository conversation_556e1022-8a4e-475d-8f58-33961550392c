# frozen_string_literal: true

# Google Cloud configuration for practice-specific AI services
# Each practice must upload their own Google service account JSON file
# No global fallback - if practice doesn't have service account, Google AI won't work

Rails.logger.info "Google AI services require per-practice service account JSON files"
Rails.logger.info "Upload service account files via Admin → General Settings → Practices"

# Set default Google Cloud settings
ENV['GOOGLE_CLOUD_LOCATION'] ||= 'us-central1'
ENV['GOOGLE_CLOUD_MODEL_ID'] ||= 'gemini-2.0-flash-001'

# Create tmp directory for practice-specific credentials if it doesn't exist
credentials_tmp_dir = Rails.root.join('tmp', 'google_credentials')
FileUtils.mkdir_p(credentials_tmp_dir) unless Dir.exist?(credentials_tmp_dir)
