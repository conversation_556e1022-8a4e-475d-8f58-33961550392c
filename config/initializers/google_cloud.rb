# frozen_string_literal: true

# Set up Google Cloud credentials for Gemini AI service
# This provides global fallback credentials when practices don't have their own service account
credentials_path = Rails.root.join('config', 'credentials', 'upod-385521-48287f0793fe.json')

if File.exist?(credentials_path)
  # Only set if not already set (allows practice-specific credentials to take precedence)
  ENV['GOOGLE_APPLICATION_CREDENTIALS'] ||= credentials_path.to_s
  Rails.logger.info "Global Google Cloud credentials available at: #{credentials_path}"
else
  Rails.logger.warn "Global Google Cloud credentials file not found at: #{credentials_path}"
  Rails.logger.info "Practices will need to upload their own Google service account JSON files"
end

# Set default Google Cloud settings if not already set
ENV['GOOGLE_CLOUD_PROJECT_ID'] ||= 'upod-385521'
ENV['GOOGLE_CLOUD_LOCATION'] ||= 'us-central1'  # Changed from europe-central2 to us-central1
ENV['GOOGLE_CLOUD_MODEL_ID'] ||= 'gemini-2.0-flash-001'

# Create tmp directory for practice-specific credentials if it doesn't exist
credentials_tmp_dir = Rails.root.join('tmp', 'google_credentials')
FileUtils.mkdir_p(credentials_tmp_dir) unless Dir.exist?(credentials_tmp_dir)
