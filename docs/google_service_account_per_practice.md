# Google Service Account Per Practice

This document describes the implementation of practice-specific Google service account credentials for AI services.

## Overview

Each practice **must** upload their own Google Cloud service account JSON file to use Google AI services (Gemini). This ensures:

- Each practice uses their own Google Cloud project and billing
- Complete isolation of AI service usage between practices
- Data sovereignty and security
- Practice-specific AI configurations
- **No Google AI services without uploaded service account**

## Implementation Details

### Database Changes

- Added `google_service_account_json` (text) field to the `practices` table
- Stores the complete JSON content of the Google service account file

### Practice Model Methods

- `google_service_account_configured?` - Check if practice has uploaded a service account file
- `google_service_account_valid?` - Validate that the JSON contains required fields
- `google_service_account_data` - Parse and return the JSON data
- `google_project_id` - Extract project ID from service account
- `google_client_email` - Extract client email from service account
- `google_credentials_file_path` - Generate temporary credentials file for Google client libraries
- `cleanup_google_credentials_file` - Remove temporary credentials file

### Admin Interface

#### Practice Information Form
- Added Google service account JSON file upload field
- Shows current configuration status (configured/not configured)
- Displays project ID and client email when configured
- File validation on upload

#### File Upload Validation
- Validates JSON format
- Checks for required fields: `type`, `project_id`, `private_key`, `client_email`
- Ensures `type` is "service_account"
- Provides user-friendly error messages

### AI Service Integration

#### GeminiService Updates
- Requires practice to have valid service account uploaded
- Uses only practice-specific credentials (no fallback)
- Returns `nil` if practice doesn't have valid service account
- Generates temporary credential files for Google client libraries
- Logs credential availability and usage

### File Management

#### Temporary Credentials Files
- Stored in `tmp/google_credentials/` directory
- Named as `practice_{id}_google_credentials.json`
- Generated on-demand and cached
- Automatically cleaned up when practice is destroyed
- Only written if content changes (performance optimization)

## Usage Instructions

### For Practice Administrators

1. **Obtain Google Service Account JSON**
   - Create a Google Cloud project
   - Enable required APIs (Vertex AI, etc.)
   - Create a service account with appropriate permissions
   - Download the JSON key file

2. **Upload to Practice**
   - Go to Admin → General Settings → Practices
   - Edit practice information
   - In "System Integration" section, find "Google AI Configuration"
   - Upload the JSON file using the file input
   - Save the practice

3. **Verify Configuration**
   - Check that the status shows "Google Service Account Configured"
   - Verify project ID and client email are displayed correctly

### For Developers

#### Using Practice-Specific Credentials

```ruby
# Initialize Gemini service with practice
practice = Practice.find(id)
gemini_service = Ai::GeminiService.new(practice)

# Service automatically uses practice credentials if available
response = gemini_service.generate_text("Your prompt here")
```

#### Checking Configuration

```ruby
practice = Practice.find(id)

# Check if configured
if practice.google_service_account_configured?
  puts "Project: #{practice.google_project_id}"
  puts "Email: #{practice.google_client_email}"
  puts "Valid: #{practice.google_service_account_valid?}"
end
```

## Security Considerations

- Service account JSON files contain sensitive credentials
- Files are stored in the database (encrypted at rest)
- Temporary files are created in `tmp/` directory (not version controlled)
- Temporary files are cleaned up automatically
- Access is restricted to admin users only

## Breaking Changes

- **No global service account fallback** - each practice must upload their own
- Practices without uploaded service accounts cannot use Google AI services
- Global service account configuration has been removed
- AI services return error messages when no service account is configured

## Troubleshooting

### Common Issues

1. **Invalid JSON Format**
   - Ensure the uploaded file is valid JSON
   - Check that it's the service account key file (not other Google Cloud files)

2. **Missing Required Fields**
   - Verify the JSON contains: `type`, `project_id`, `private_key`, `client_email`
   - Ensure `type` field equals "service_account"

3. **Permission Errors**
   - Verify the service account has required permissions in Google Cloud
   - Check that Vertex AI API is enabled in the project

4. **File Upload Errors**
   - Check file size limits
   - Ensure file has `.json` extension
   - Verify admin user has permission to edit practices

### Logging

The system logs which credential source is being used:
- "Using practice-specific Google credentials for practice {id}"
- "Using global Google credentials (practice {id} has no valid service account)"

Check Rails logs for credential-related messages when troubleshooting.
