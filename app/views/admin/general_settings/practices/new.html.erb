<div class="flex-1 flex flex-col transition-all duration-300 ease-in-out ml-72 p-8 main-content-area bg-gray-50">
  <%= render 'admin/general_settings/side_panel' %>

  <div class="flex items-center justify-between mb-8">
    <h1 class="text-2xl font-semibold text-slate-800">Create Practice</h1>
    <%= link_to admin_general_settings_practices_path, class: "inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-medium transition-colors duration-200" do %>
      <span class="material-symbols-outlined mr-2 text-lg">arrow_back</span>
      Back to Practices
    <% end %>
  </div>

  <div class="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
    <%= form_for [:admin_general_settings, @practice] do |form| %>
      <% if @practice.errors.any? %>
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 m-6">
          <div class="flex items-center">
            <span class="material-symbols-outlined text-red-600 mr-3">error</span>
            <div>
              <h3 class="text-red-800 font-medium">
                <%= pluralize(@practice.errors.count, "error") %> prohibited this practice from being saved:
              </h3>
              <ul class="text-red-700 text-sm mt-2 ml-4 list-disc">
                <% @practice.errors.full_messages.each do |message| %>
                  <li><%= message %></li>
                <% end %>
              </ul>
            </div>
          </div>
        </div>
      <% end %>

      <div class="border-b border-gray-200 px-6 pt-6">
        <nav class="flex space-x-8" id="practice-tabs">
          <button type="button" class="practice-tab-btn flex items-center py-4 px-1 border-b-2 border-blue-600 text-blue-600 font-medium text-sm active" data-tab="information-tab">
            <span class="material-symbols-outlined mr-2 text-lg">info</span>
            Information
          </button>
          <button type="button" class="practice-tab-btn flex items-center py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium text-sm" data-tab="opening-hours-tab">
            <span class="material-symbols-outlined mr-2 text-lg">schedule</span>
            Opening Hours
          </button>
          <button type="button" class="practice-tab-btn flex items-center py-4 px-1 border-b-2 border-transparent text-gray-400 cursor-not-allowed font-medium text-sm opacity-50" disabled>
            <span class="material-symbols-outlined mr-2 text-lg">medical_services</span>
            NHS Contracts
          </button>
          <button type="button" class="practice-tab-btn flex items-center py-4 px-1 border-b-2 border-transparent text-gray-400 cursor-not-allowed font-medium text-sm opacity-50" disabled>
            <span class="material-symbols-outlined mr-2 text-lg">credit_card</span>
            Payment Processing
          </button>
          <button type="button" class="practice-tab-btn flex items-center py-4 px-1 border-b-2 border-transparent text-gray-400 cursor-not-allowed font-medium text-sm opacity-50" disabled>
            <span class="material-symbols-outlined mr-2 text-lg">chat</span>
            Communications
          </button>
        </nav>
      </div>

      <div class="tab-content">
        <div class="tab-pane active p-6" id="information-tab">
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-2 space-y-6">
              <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Practice Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <%= form.label :name, "Practice name", class: "block text-sm font-medium text-gray-700 mb-2" %>
                    <%= form.text_field :name, class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" %>
                  </div>
                  <div>
                    <%= form.label :website, "Practice website", class: "block text-sm font-medium text-gray-700 mb-2" %>
                    <%= form.url_field :website, class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500", placeholder: "https://www.example.com" %>
                  </div>
                  <div>
                    <%= form.label :email, "Email address", class: "block text-sm font-medium text-gray-700 mb-2" %>
                    <%= form.email_field :email, class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" %>
                  </div>
                  <div>
                    <%= form.label :phone, "Contact number", class: "block text-sm font-medium text-gray-700 mb-2" %>
                    <%= form.phone_field :phone, class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 tel-input" %>
                  </div>
                </div>
              </div>

              <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Address</h3>
                <div class="space-y-4">
                  <div>
                    <%= form.label :address_line_1, "Address line 1", class: "block text-sm font-medium text-gray-700 mb-2" %>
                    <%= form.text_field :address_line_1, class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" %>
                  </div>
                  <div>
                    <%= form.label :address_line_2, "Address line 2", class: "block text-sm font-medium text-gray-700 mb-2" %>
                    <%= form.text_field :address_line_2, class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" %>
                  </div>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <%= form.label :city, "City", class: "block text-sm font-medium text-gray-700 mb-2" %>
                      <%= form.text_field :city, class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" %>
                    </div>
                    <div>
                      <%= form.label :postcode, "Postcode", class: "block text-sm font-medium text-gray-700 mb-2" %>
                      <%= form.text_field :postcode, class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" %>
                    </div>
                  </div>
                </div>
              </div>

              <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Branding</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <%= form.label :primary_color, "Primary colour", class: "block text-sm font-medium text-gray-700 mb-2" %>
                    <div class="flex items-center space-x-3">
                      <%= form.text_field :primary_color, class: "flex-1 px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 color-picker w-full", placeholder: "#000000" %>
                      <div class="w-10 h-10 border border-gray-300 rounded-lg color-preview" data-color-input="practice_primary_color" style="background-color: <%= @practice.primary_color.present? ? @practice.primary_color : '#ffffff' %>"></div>
                    </div>
                  </div>
                  <div>
                    <%= form.label :secondary_color, "Secondary colour", class: "block text-sm font-medium text-gray-700 mb-2" %>
                    <div class="flex items-center space-x-3">
                      <%= form.text_field :secondary_color, class: "flex-1 px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 color-picker w-full", placeholder: "#000000" %>
                      <div class="w-10 h-10 border border-gray-300 rounded-lg color-preview" data-color-input="practice_secondary_color" style="background-color: <%= @practice.secondary_color.present? ? @practice.secondary_color : '#ffffff' %>"></div>
                    </div>
                  </div>
                  <div>
                    <%= form.label :tertiary_color, "Tertiary colour", class: "block text-sm font-medium text-gray-700 mb-2" %>
                    <div class="flex items-center space-x-3">
                      <%= form.text_field :tertiary_color, class: "flex-1 px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 color-picker w-full", placeholder: "#000000" %>
                      <div class="w-10 h-10 border border-gray-300 rounded-lg color-preview" data-color-input="practice_tertiary_color" style="background-color: <%= @practice.tertiary_color.present? ? @practice.tertiary_color : '#ffffff' %>"></div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">System Integration</h3>
                <div class="space-y-4">
                  <div>
                    <%= form.label :azure_url, "Azure URL", class: "block text-sm font-medium text-gray-700 mb-2" %>
                    <%= form.text_field :azure_url, class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" %>
                  </div>
                  <div>
                    <%= form.label :azure_token, "Azure Token", class: "block text-sm font-medium text-gray-700 mb-2" %>
                    <%= form.text_field :azure_token, class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" %>
                  </div>

                  <!-- Google Service Account Section -->
                  <div class="border-t border-gray-200 pt-4">
                    <h4 class="text-md font-medium text-gray-900 mb-3">Google AI Configuration</h4>
                    <div>
                      <%= form.label :google_service_account_file, "Google Service Account JSON", class: "block text-sm font-medium text-gray-700 mb-2" %>
                      <%= form.file_field :google_service_account_file,
                            accept: ".json",
                            class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100" %>
                      <p class="text-xs text-red-600 mt-1 font-medium">Required: Upload your Google Cloud service account JSON file to enable AI services</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="space-y-6">
              <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Practice Logo</h3>
                <div class="text-center">
                  <img src="<%= @practice.logo.attached? ? @practice.logo.url : "" %>" alt="" class="fd-preview mx-auto mb-4 max-w-full h-32 object-contain rounded-lg border border-gray-200 bg-white" />
                  <%= form.file_field :logo, class: "hidden" %>
                  <div class="file-drop border-2 border-dashed border-gray-300 rounded-lg p-4 mb-4 text-center hover:border-gray-400 transition-colors">
                    <span class="material-symbols-outlined text-gray-400 text-2xl mb-2 block">cloud_upload</span>
                    <p class="text-sm text-gray-600">Drop logo here or click to browse</p>
                  </div>
                  <button type="button" class="fd-button w-full bg-blue-100 hover:bg-blue-200 text-blue-700 py-2 px-4 rounded-lg font-medium transition-colors">
                    Change Logo
                  </button>
                </div>
              </div>

              <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Practice Image</h3>
                <div class="text-center">
                  <img src="<%= @practice.image.attached? ? @practice.image.url : "" %>" alt="" class="fd-preview mx-auto mb-4 max-w-full h-32 object-contain rounded-lg border border-gray-200 bg-white" />
                  <%= form.file_field :image, class: "hidden" %>
                  <div class="file-drop border-2 border-dashed border-gray-300 rounded-lg p-4 mb-4 text-center hover:border-gray-400 transition-colors">
                    <span class="material-symbols-outlined text-gray-400 text-2xl mb-2 block">cloud_upload</span>
                    <p class="text-sm text-gray-600">Drop image here or click to browse</p>
                  </div>
                  <button type="button" class="fd-button w-full bg-blue-100 hover:bg-blue-200 text-blue-700 py-2 px-4 rounded-lg font-medium transition-colors">
                    Change Image
                  </button>
                </div>
              </div>

              <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Favicon Image</h3>
                <div class="text-center">
                  <img src="<%= @practice.favicon_image.attached? ? @practice.favicon_image.url : "" %>" alt="" class="fd-preview mx-auto mb-4 max-w-full h-32 object-contain rounded-lg border border-gray-200 bg-white" />
                  <%= form.file_field :favicon_image, class: "hidden" %>
                  <div class="file-drop border-2 border-dashed border-gray-300 rounded-lg p-4 mb-4 text-center hover:border-gray-400 transition-colors">
                    <span class="material-symbols-outlined text-gray-400 text-2xl mb-2 block">cloud_upload</span>
                    <p class="text-sm text-gray-600">Drop image here or click to browse</p>
                  </div>
                  <button type="button" class="fd-button w-full bg-blue-100 hover:bg-blue-200 text-blue-700 py-2 px-4 rounded-lg font-medium transition-colors">
                    Change Favicon Image
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="tab-pane hidden p-6" id="opening-hours-tab">
          <div class="max-w-5xl">
            <div class="mb-8">
              <h3 class="text-xl font-semibold text-gray-900 mb-2">Opening Hours</h3>
              <p class="text-sm text-gray-600">Set your practice opening hours for each day of the week</p>
            </div>

            <div class="bg-white rounded-xl border border-gray-200 overflow-hidden">
              <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                  <h4 class="font-medium text-gray-900">Weekly Schedule</h4>
                  <div class="flex items-center space-x-2 text-sm text-gray-500">
                    <span class="material-symbols-outlined text-sm">schedule</span>
                    <span>24-hour format</span>
                  </div>
                </div>
              </div>

              <div class="divide-y divide-gray-100">
                <%= form.fields_for :opening_hours do |o| %>
                  <% Date::DAYNAMES.each_with_index do |day, index| %>
                    <div class="px-6 py-5 hover:bg-gray-50 transition-colors">
                      <%= o.fields_for index.to_s do |f| %>
                        <div class="grid grid-cols-12 items-center gap-4">
                          <div class="col-span-2">
                            <h5 class="font-medium text-gray-900"><%= day %></h5>
                          </div>

                          <div class="col-span-8 opening-hours-times flex items-center justify-start space-x-4" data-day="<%= index %>">
                            <div class="flex items-center space-x-2">
                              <span class="material-symbols-outlined text-gray-400 text-sm">schedule</span>
                              <%= f.time_field :start,
                                  class: "w-32 px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm",
                                  value: (@practice.opening_hours[index.to_s] || {})["start"],
                                  placeholder: "09:00" %>
                            </div>

                            <div class="flex items-center space-x-2">
                              <span class="text-gray-400 font-medium">to</span>
                              <span class="material-symbols-outlined text-gray-400 text-sm">schedule</span>
                              <%= f.time_field :end,
                                  class: "w-32 px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm",
                                  value: (@practice.opening_hours[index.to_s] || {})["end"],
                                  placeholder: "17:00" %>
                            </div>
                          </div>

                          <div class="col-span-2 flex justify-end">
                            <div class="flex items-center bg-gray-100 rounded-lg p-2">
                              <%= f.check_box :closed,
                                  class: "sr-only opening-hours-checkbox",
                                  checked: (@practice.opening_hours[index.to_s] || {})["closed"] == "1",
                                  data: { day: index },
                                  id: "closed_#{index}" %>
                              <%= f.label :closed,
                                  class: "flex items-center cursor-pointer select-none",
                                  for: "closed_#{index}" do %>
                                <span class="material-symbols-outlined text-gray-400 mr-2 text-sm">block</span>
                                <span class="text-sm font-medium text-gray-700">Closed</span>
                              <% end %>
                            </div>
                          </div>
                        </div>
                      <% end %>
                    </div>
                  <% end %>
                <% end %>
              </div>
            </div>
          </div>
        </div>

        <div class="tab-pane hidden p-6" id="nhs-contracts-tab">
          <div class="text-center py-12">
            <span class="material-symbols-outlined text-gray-400 text-6xl mb-4 block">medical_services</span>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">NHS Contracts</h3>
            <p class="text-gray-600">NHS contract management coming soon</p>
          </div>
        </div>

        <div class="tab-pane hidden p-6" id="payment-processing-tab">
          <div class="text-center py-12">
            <span class="material-symbols-outlined text-gray-400 text-6xl mb-4 block">credit_card</span>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Payment Processing</h3>
            <p class="text-gray-600">Payment processing setup coming soon</p>
          </div>
        </div>

        <div class="tab-pane hidden p-6" id="communications-tab">
          <div class="text-center py-12">
            <span class="material-symbols-outlined text-gray-400 text-6xl mb-4 block">chat</span>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Communications</h3>
            <p class="text-gray-600">Communication settings coming soon</p>
          </div>
        </div>
      </div>

      <div class="bg-gray-50 px-6 py-4 border-t border-gray-200 flex justify-between items-center">
        <%= link_to "Cancel", admin_general_settings_practices_path, class: "px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-colors" %>
        <%= form.submit 'Create Practice', class: 'px-6 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg font-medium transition-colors focus:ring-2 focus:ring-blue-500 focus:ring-offset-2' %>
      </div>
    <% end %>
  </div>
</div>
