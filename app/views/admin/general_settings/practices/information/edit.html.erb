<div class="flex-1 flex flex-col transition-all duration-300 ease-in-out ml-72 p-8 main-content-area bg-gray-50">
  <%= render 'admin/general_settings/side_panel' %>

  <div class="flex items-center justify-between mb-8">
    <div>
      <h1 class="text-2xl font-semibold text-slate-800">Edit Practice Information</h1>
      <p class="text-sm text-gray-600 mt-1">Update your practice details and branding settings</p>
    </div>
    <%= link_to admin_general_settings_practices_path, class: "flex items-center space-x-2 px-4 py-2 text-gray-700 bg-white hover:bg-gray-50 rounded-full border border-gray-300 font-medium transition-colors" do %>
      <span class="material-symbols-outlined text-sm">arrow_back</span>
      <span>Back to Practices</span>
    <% end %>
  </div>

  <%= render 'admin/general_settings/practices/edit_navigation', active_tab: 'information' %>

  <div class="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
    <%= form_for @practice,
                 url: admin_general_settings_practice_information_path(@practice),
                 method: :patch do |form| %>
      <% if @practice.errors.any? %>
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 m-6">
          <div class="flex items-center">
            <span class="material-symbols-outlined text-red-600 mr-3">error</span>
            <div>
              <h4 class="text-sm font-medium text-red-800">There were some problems with your submission</h4>
              <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                <% @practice.errors.full_messages.each do |message| %>
                  <li><%= message %></li>
                <% end %>
              </ul>
            </div>
          </div>
        </div>
      <% end %>

      <div class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div class="lg:col-span-2 space-y-6">
            <div class="bg-gray-50 rounded-lg p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Practice Information</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <%= form.label :name, "Practice name", class: "block text-sm font-medium text-gray-700 mb-2" %>
                  <%= form.text_field :name, class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" %>
                </div>
                <div>
                  <%= form.label :website, "Practice website", class: "block text-sm font-medium text-gray-700 mb-2" %>
                  <%= form.text_field :website, class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500", placeholder: "https://example.com" %>
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-2">
                <div>
                  <%= form.label :num_surgeries, "Number of surgeries", class: "block text-sm font-medium text-gray-700 mb-2" %>
                  <%= form.number_field :num_surgeries, class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" %>
                </div>
              </div>
            </div>

            <div class="bg-gray-50 rounded-lg p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Address</h3>
              <div class="space-y-4">
                <%= form.text_field :address_line_1, class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500", placeholder: "Address line 1" %>
                <%= form.text_field :address_line_2, class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500", placeholder: "Address line 2" %>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <%= form.text_field :city, class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500", placeholder: "City" %>
                  <%= form.text_field :postcode, class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500", placeholder: "Postcode" %>
                </div>
              </div>
            </div>

            <div class="bg-gray-50 rounded-lg p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <%= form.label :email, "Email address", class: "block text-sm font-medium text-gray-700 mb-2" %>
                  <%= form.email_field :email, class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" %>
                </div>
                <div>
                  <%= form.label :phone, "Contact number", class: "block text-sm font-medium text-gray-700 mb-2" %>
                  <%= form.phone_field :phone, class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 tel-input" %>
                </div>
              </div>
            </div>

            <div class="bg-gray-50 rounded-lg p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Branding</h3>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <%= form.label :primary_color, "Primary colour", class: "block text-sm font-medium text-gray-700 mb-2" %>
                  <div class="flex items-center space-x-3">
                    <%= form.text_field :primary_color, class: "flex-1 px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 color-picker w-full", placeholder: "#000000" %>
                    <div class="w-10 h-10 border border-gray-300 rounded-lg color-preview" data-color-input="practice_primary_color" style="background-color: <%= @practice.primary_color.present? ? @practice.primary_color : '#ffffff' %>"></div>
                  </div>
                </div>
                <div>
                  <%= form.label :secondary_color, "Secondary colour", class: "block text-sm font-medium text-gray-700 mb-2" %>
                  <div class="flex items-center space-x-3">
                    <%= form.text_field :secondary_color, class: "flex-1 px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 color-picker w-full", placeholder: "#000000" %>
                    <div class="w-10 h-10 border border-gray-300 rounded-lg color-preview" data-color-input="practice_secondary_color" style="background-color: <%= @practice.secondary_color.present? ? @practice.secondary_color : '#ffffff' %>"></div>
                  </div>
                </div>
                <div>
                  <%= form.label :tertiary_color, "Tertiary colour", class: "block text-sm font-medium text-gray-700 mb-2" %>
                  <div class="flex items-center space-x-3">
                    <%= form.text_field :tertiary_color, class: "flex-1 px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 color-picker w-full", placeholder: "#000000" %>
                    <div class="w-10 h-10 border border-gray-300 rounded-lg color-preview" data-color-input="practice_tertiary_color" style="background-color: <%= @practice.tertiary_color.present? ? @practice.tertiary_color : '#ffffff' %>"></div>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-gray-50 rounded-lg p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">System Integration</h3>
              <div class="space-y-4">
                <div>
                  <%= form.label :azure_url, "Azure URL", class: "block text-sm font-medium text-gray-700 mb-2" %>
                  <%= form.text_field :azure_url, class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500", placeholder: "https://your-azure-instance.com" %>
                </div>
                <div>
                  <%= form.label :azure_token, "Azure Token", class: "block text-sm font-medium text-gray-700 mb-2" %>
                  <%= form.text_field :azure_token, class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500", placeholder: "Your Azure authentication token" %>
                </div>

                <!-- Google Service Account Section -->
                <div class="border-t border-gray-200 pt-4">
                  <h4 class="text-md font-medium text-gray-900 mb-3">Google AI Configuration</h4>
                  <div class="space-y-3">
                    <div>
                      <%= form.label :google_service_account_file, "Google Service Account JSON", class: "block text-sm font-medium text-gray-700 mb-2" %>
                      <%= form.file_field :google_service_account_file,
                            accept: ".json",
                            class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100" %>
                      <p class="text-xs text-gray-500 mt-1">Upload your Google Cloud service account JSON file for AI services</p>
                    </div>

                    <% if @practice.google_service_account_configured? %>
                      <div class="flex items-center space-x-2 p-3 bg-green-50 rounded-lg border border-green-200">
                        <span class="material-symbols-outlined text-green-600 text-sm">check_circle</span>
                        <div class="flex-1">
                          <p class="text-sm font-medium text-green-800">Google Service Account Configured</p>
                          <p class="text-xs text-green-600">
                            Project: <%= @practice.google_project_id %> |
                            Email: <%= @practice.google_client_email %>
                          </p>
                        </div>
                      </div>
                    <% else %>
                      <div class="flex items-center space-x-2 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                        <span class="material-symbols-outlined text-yellow-600 text-sm">warning</span>
                        <p class="text-sm text-yellow-800">No Google service account configured. Upload a JSON file to enable Google AI services.</p>
                      </div>
                    <% end %>
                  </div>
                </div>

                <% if @practice.id.present? %>
                  <div class="flex items-center space-x-3 p-4 bg-blue-50 rounded-lg">
                    <%= form.check_box :two_factor_enabled, class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded", checked: @practice.two_factor_enabled, data: { "practice-id": @practice.id } %>
                    <%= form.label :two_factor_enabled, "Enable Patient Two Factor Authentication", class: "text-sm font-medium text-gray-900" %>
                  </div>
                <% end %>
              </div>
            </div>
          </div>

          <div class="space-y-6">
            <div class="bg-gray-50 rounded-lg p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Practice Logo</h3>
              <div class="text-center">
                <div class="mb-4">
                  <img src="<%= @practice.logo.attached? ? @practice.logo.url : "" %>" alt="Practice Logo" class="fd-preview mx-auto max-w-full h-32 object-contain rounded-lg border border-gray-200 bg-white"/>
                </div>
                <%= form.file_field :logo, class: "d-none" %>
                <div class="file-drop border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-500 transition-colors">
                </div>
              </div>
            </div>

            <div class="bg-gray-50 rounded-lg p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Practice Image</h3>
              <div class="text-center">
                <div class="mb-4">
                  <img src="<%= @practice.image.attached? ? @practice.image.url : "" %>" alt="Practice Image" class="fd-preview mx-auto max-w-full h-32 object-contain rounded-lg border border-gray-200 bg-white"/>
                </div>
                <%= form.file_field :image, class: "d-none" %>
                <div class="file-drop border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-500 transition-colors">
                </div>
              </div>
            </div>

            <div class="bg-gray-50 rounded-lg p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Favicon Image</h3>
              <div class="text-center">
                <div class="mb-4">
                  <img src="<%= @practice.favicon_image.attached? ? @practice.favicon_image.url : "" %>" alt="Favicon Image" class="fd-preview mx-auto max-w-full h-32 object-contain rounded-lg border border-gray-200 bg-white"/>
                </div>
                <%= form.file_field :favicon_image, class: "d-none" %>
                <div class="file-drop border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-500 transition-colors">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-gray-50 px-6 py-4 border-t border-gray-200 flex justify-between items-center">
        <%= link_to "Cancel", admin_general_settings_practices_path, class: "px-4 py-2 text-gray-700 bg-white hover:bg-gray-100 rounded-full font-medium transition-colors border border-gray-300" %>
        <%= form.submit 'Save Changes', class: 'px-6 py-2 bg-blue-100 hover:bg-blue-200 text-blue-800 rounded-full font-medium transition-colors focus:ring-2 focus:ring-blue-500 focus:ring-offset-2' %>
      </div>
    <% end %>
  </div>
</div>
