# frozen_string_literal: true

require 'google/cloud/ai_platform/v1'
require 'httparty'

module Ai
  class GeminiService < BaseService
    # Direct API endpoint for API key fallback
    DIRECT_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models'

    def initialize(practice)
      super(practice)
      @practice = practice
      @location = ENV['GOOGLE_CLOUD_LOCATION'] || 'europe-west1'
      @model_id = 'gemini-2.5-pro'
      @api_key = ENV['GOOGLE_CLOUD_API_KEY']

      # Set up practice-specific credentials (but don't initialize client yet)
      setup_credentials

      Rails.logger.info "GeminiService initialized for practice #{@practice&.id}"
      Rails.logger.info "Google service available: #{google_service_available?}"
      Rails.logger.info "Project ID: #{@project_id}" if @project_id
    end

    private

    def setup_credentials
      if @practice&.google_service_account_configured? && @practice.google_service_account_valid?
        # Use practice-specific credentials
        @project_id = @practice.google_project_id
        @credentials_file = @practice.google_credentials_file_path

        # Set environment variable for this request
        ENV['GOOGLE_APPLICATION_CREDENTIALS'] = @credentials_file

        Rails.logger.info "Using practice-specific Google credentials for practice #{@practice.id}"
      else
        # No credentials available - Google AI won't work
        @project_id = nil
        @credentials_file = nil

        Rails.logger.warn "Practice #{@practice&.id} has no valid Google service account - Google AI services unavailable"
      end
    end

    public

    # Check if Google service is available for this practice
    def google_service_available?
      @practice&.google_service_account_configured? && @practice.google_service_account_valid?
    end

    # Check if service account authentication is available
    def service_account_available?
      @credentials_file.present? &&
        File.exist?(@credentials_file) &&
        ENV['GOOGLE_CLOUD_API_KEY'].nil?
    end

    # Lazy initialization of Google client
    def google_client
      return @client if @client

      if service_account_available?
        begin
          @client = Google::Cloud::AIPlatform::V1::PredictionService::Client.new
        rescue StandardError => e
          Rails.logger.error "Failed to initialize Google client for practice #{@practice&.id}: #{e.message}"
          @client = nil
        end
      end

      @client
    end

    def generate_text(prompt, model: nil, temperature: 0.7, max_tokens: 8192, top_p: 1.0)
      return nil unless google_service_available?

      model ||= @model_id

      if service_account_available? && google_client
        generate_text_vertex_ai(prompt, model, temperature, max_tokens, top_p)
      else
        generate_text_direct_api(prompt, model, temperature, max_tokens, top_p)
      end
    end

    # Generate text using Vertex AI with service account auth
    def generate_text_vertex_ai(prompt, model, temperature, max_tokens, top_p)
      # Format the model name
      model_name = "projects/#{@project_id}/locations/#{@location}/publishers/google/models/#{model}"

      # Create the GenerateContentRequest object
      request = Google::Cloud::AIPlatform::V1::GenerateContentRequest.new
      request.model = model_name

      # Add content part
      content = Google::Cloud::AIPlatform::V1::Content.new
      content.role = 'user'

      text_part = Google::Cloud::AIPlatform::V1::Part.new
      text_part.text = prompt
      content.parts << text_part

      request.contents << content

      # Set generation parameters
      request.generation_config = Google::Cloud::AIPlatform::V1::GenerationConfig.new
      request.generation_config.temperature = temperature
      request.generation_config.max_output_tokens = max_tokens
      request.generation_config.top_p = top_p

      begin
        # Make the request
        response = google_client.generate_content(request)

        # Extract the response text
        if response.candidates.any? && response.candidates.first.content.parts.any?
          response.candidates.first.content.parts.first.text
        else
          Rails.logger.error 'No content in Gemini response'
          nil
        end
      rescue StandardError => e
        Rails.logger.error "Error in Gemini Vertex AI interaction: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
        nil
      end
    end

    # Generate text using direct API with API key
    def generate_text_direct_api(prompt, model, temperature, max_tokens, top_p)
      return nil unless @api_key

      url = "#{DIRECT_API_URL}/#{model}:generateContent?key=#{@api_key}"

      body = {
        contents: [
          {
            role: 'user',
            parts: [
              { text: prompt }
            ]
          }
        ],
        generationConfig: {
          temperature: temperature,
          maxOutputTokens: max_tokens,
          topP: top_p,
          topK: 32,
          response_mime_type: 'text/plain' # Ensure plain text response
        }
      }

      begin
        response = HTTParty.post(
          url,
          headers: { 'Content-Type' => 'application/json' },
          body: body.to_json
        )

        if response.success?
          response.parsed_response.dig('candidates', 0, 'content', 'parts', 0, 'text')
        else
          Rails.logger.error "Gemini API error: #{response.code} - #{response.body}"
          nil
        end
      rescue StandardError => e
        Rails.logger.error "Error in Gemini Direct API interaction: #{e.message}"
        nil
      end
    end

    def chat_completion(messages, model: nil, temperature: 0.7, max_tokens: 8192, top_p: 1.0)
      return nil unless google_service_available?

      model ||= @model_id

      if service_account_available? && google_client
        chat_completion_vertex_ai(messages, model, temperature, max_tokens, top_p)
      else
        chat_completion_direct_api(messages, model, temperature, max_tokens, top_p)
      end
    end

    # Chat completion using Vertex AI with service account auth
    def chat_completion_vertex_ai(messages, model, temperature, _max_tokens, top_p)
      model_name = "projects/#{@project_id}/locations/#{@location}/publishers/google/models/#{model}"

      request = Google::Cloud::AIPlatform::V1::GenerateContentRequest.new
      request.model = model_name

      # Format messages
      messages.each do |message|
        role = message[:role] || message['role']
        content_text = message[:content] || message['content']

        # Gemini expects either 'user' or 'model'
        api_role = case role
                   when 'assistant' then 'model'
                   else 'user'
                   end

        content = Google::Cloud::AIPlatform::V1::Content.new(role: api_role)
        content.parts << Google::Cloud::AIPlatform::V1::Part.new(text: content_text)
        request.contents << content
      end

      # Generation config
      request.generation_config = Google::Cloud::AIPlatform::V1::GenerationConfig.new(
        temperature: temperature,
        max_output_tokens: 8192,
        top_p: top_p
      )

      # Optional: disable safety filters to prevent content blocks
      request.safety_settings.concat([
                                       Google::Cloud::AIPlatform::V1::SafetySetting.new(category: :HARM_CATEGORY_HATE_SPEECH,
                                                                                        threshold: :BLOCK_NONE),
                                       Google::Cloud::AIPlatform::V1::SafetySetting.new(category: :HARM_CATEGORY_DANGEROUS_CONTENT,
                                                                                        threshold: :BLOCK_NONE),
                                       Google::Cloud::AIPlatform::V1::SafetySetting.new(category: :HARM_CATEGORY_SEXUALLY_EXPLICIT,
                                                                                        threshold: :BLOCK_NONE),
                                       Google::Cloud::AIPlatform::V1::SafetySetting.new(category: :HARM_CATEGORY_HARASSMENT,
                                                                                        threshold: :BLOCK_NONE)
                                     ])

      begin
        response = google_client.generate_content(request)

        if response.candidates.any? && response.candidates.first.content.parts.any?
          response.candidates.first.content.parts.first.text
        else
          Rails.logger.warn "Gemini returned no content. Finish reason: #{response.candidates.first&.finish_reason}"
          Rails.logger.warn "Full response: #{response.to_json}"
          nil
        end
      rescue StandardError => e
        Rails.logger.error "Error during Gemini request: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
        nil
      end
    end

    # Chat completion using direct API with API key
    def chat_completion_direct_api(messages, model, temperature, max_tokens, top_p)
      return nil unless @api_key

      url = "#{DIRECT_API_URL}/#{model}:generateContent?key=#{@api_key}"

      # Format messages for direct API
      contents = []

      messages.each do |message|
        role = message[:role] || message['role']
        content_text = message[:content] || message['content']

        # Convert roles to match Gemini API expectations
        api_role = case role
                   when 'system' then 'user'
                   when 'assistant' then 'model'
                   else 'user'
                   end

        contents << {
          role: api_role,
          parts: [{ text: content_text }]
        }
      end

      body = {
        contents: contents,
        generationConfig: {
          temperature: temperature,
          maxOutputTokens: max_tokens,
          topP: top_p,
          topK: 32,
          response_mime_type: 'text/plain' # Ensure plain text response
        }
      }

      begin
        response = HTTParty.post(
          url,
          headers: { 'Content-Type' => 'application/json' },
          body: body.to_json
        )

        if response.success?
          response.parsed_response.dig('candidates', 0, 'content', 'parts', 0, 'text')
        else
          Rails.logger.error "Gemini API error: #{response.code} - #{response.body}"
          nil
        end
      rescue StandardError => e
        Rails.logger.error "Error in Gemini chat interaction via Direct API: #{e.message}"
        nil
      end
    end
  end
end
