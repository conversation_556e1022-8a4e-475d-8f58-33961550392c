# Google Gemini AI Service with Vertex AI

This directory contains services for interacting with Google's Gemini AI models through Google Cloud's Vertex AI API using the official `google-cloud-ai_platform` gem.

## Setup

### Practice-Specific Configuration (Recommended)

Each practice can upload their own Google service account JSON file:

1. Create a Google Cloud project for the practice
2. Enable the Vertex AI API
3. Create a service account with appropriate permissions
4. Download the service account JSON key file
5. Upload the JSON file in Admin → General Settings → Practices → Edit Practice → System Integration

### Global Configuration (Fallback)

For practices without their own service account, the system uses global credentials:

1. Set up a Google Cloud project
2. Enable the Vertex AI API
3. Set up authentication (service account with appropriate permissions)
4. Add the following environment variables to your application:

```
GOOGLE_CLOUD_PROJECT_ID=your-project-id  # Default: upod-385521
GOOGLE_CLOUD_LOCATION=your-region        # Default: us-central1
GOOGLE_CLOUD_MODEL_ID=your-model-id      # Default: gemini-2.0-flash-001
```

## Authentication

The service automatically uses practice-specific credentials when available, falling back to global credentials.

### Practice-Specific Authentication
- Upload service account JSON file through admin interface
- Credentials are automatically used for that practice's AI requests
- Each practice uses their own Google Cloud project and billing

### Global Authentication (Fallback)
- Uses the service account file in `config/credentials/`
- Set via `GOOGLE_APPLICATION_CREDENTIALS` environment variable
- Used when practice doesn't have valid service account uploaded

## Available Services

### GeminiService

The base service that handles direct communication with the Vertex AI API.

```ruby
# Initialize the service
practice = Practice.find(id)
gemini_service = Ai::GeminiService.new(practice)

# Generate text
response = gemini_service.generate_text("Your prompt here", 
                                       temperature: 0.7, 
                                       max_tokens: 8192,
                                       top_p: 1.0)

# Chat completion
messages = [
  { role: "system", content: "You are a helpful assistant." },
  { role: "user", content: "Tell me about dental procedures" }
]
response = gemini_service.chat_completion(messages, 
                                         temperature: 0.7, 
                                         max_tokens: 8192)
```

### GeminiExampleService

A higher-level service with pre-configured methods for common tasks.

```ruby
# Initialize the service
practice = Practice.find(id)
example_service = Ai::GeminiExampleService.new(practice)

# Generate a title
title = example_service.generate_title("Some text that needs a title")

# Summarize text
summary = example_service.summarize_text("Long text that needs summarization")

# Chat conversation
messages = [
  { role: "system", content: "You are a helpful assistant." },
  { role: "user", content: "Tell me about dental procedures" }
]
response = example_service.chat_conversation(messages)

# Analyze medical text
analysis = example_service.analyze_medical_text("Patient presents with symptoms of...")

# Generate patient summary
summary = example_service.generate_patient_summary(patient_data_hash)
```

## Models

The default model is `gemini-2.0-flash-001`, but you can specify other Gemini models available in your region:

- `gemini-1.0-pro` - First generation Gemini model
- `gemini-1.5-pro` - Improved Gemini model
- `gemini-2.0-flash-001` - Fast and efficient model for most tasks
- `gemini-2.0-pro-001` - Higher capability model for complex tasks

## Parameters

- `temperature` (0.0-1.0): Controls randomness. Lower values are more deterministic.
- `max_tokens`: Maximum number of tokens to generate (default: 8192).
- `top_p`: Controls diversity via nucleus sampling (default: 1.0).

## Safety Settings

The service is configured with safety settings that disable content filtering. This is appropriate for medical applications where discussions of sensitive topics may be necessary.

## Error Handling

All methods will return `nil` if an error occurs, and the error will be logged using `Rails.logger.error`.
