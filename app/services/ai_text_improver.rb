# frozen_string_literal: true

class AiTextImprover
  def initialize(practice: nil, patient: nil)
    @practice = determine_practice(practice, patient)
    @gemini_service = Ai::GeminiService.new(@practice)
  end

  private

  def determine_practice(practice, patient)
    # If practice is explicitly provided, use it
    return practice if practice.present?

    # If Current.practice is set, use it
    return Current.practice if Current.practice.present?

    # If patient is provided, use patient's default practice
    return patient.default_practice if patient.present?

    # No practice context available
    nil
  end

  def ai_unavailable_error_message
    if @practice.nil?
      'Google AI service not available. Please select a specific practice to use AI features.'
    else
      "Google AI service not available for #{@practice.name}. Please upload a Google service account JSON file for this practice."
    end
  end

  public

  def generate_title(message:)
    return { error: ai_unavailable_error_message } unless @gemini_service.send(:google_service_available?)

    system_prompt = 'You are an AI assistant. You will be given a block of text. ' \
                    'Your task is to generate a concise and relevant title for this text, ideally between 3 to 7 words. ' \
                    'Return ONLY the generated title, with no extra text, ' \
                    'no introductory phrases like \'Here is a title:\', and no quotation marks around the title itself.'
    user_prompt = message

    messages = [
      { role: 'system', content: system_prompt },
      { role: 'user', content: user_prompt }
    ]

    response = @gemini_service.chat_completion(
      messages,
      temperature: 0.7,
      max_tokens: 30 # Specific for title generation
    )

    process_response(response)
  end

  def improve_text(message:, edits: 'sound more professional')
    return { error: ai_unavailable_error_message } unless @gemini_service.send(:google_service_available?)

    system_prompt = "You will be given a user's message that they are writing and the edits that they would like to be made to it. " \
                    "Please only return the edited version with no extra text. You will be given the user's message first, " \
                    "then the edits they would like, separated by '---'."
    user_prompt = "#{message}\n---\nMake it #{edits}"

    messages = [
      { role: 'system', content: system_prompt },
      { role: 'user', content: user_prompt }
    ]

    response = @gemini_service.chat_completion(
      messages,
      temperature: 0.7,
      max_tokens: 256 # Specific for text improvement
    )

    process_response(response)
  end

  private

  def process_response(response)
    if response.is_a?(String)
      processed_response = response.gsub(/<[^>]*>/, '') # Remove HTML tags
      processed_response = processed_response.gsub(/^"|"$|^'|'$/, '') # Remove leading/trailing quotes
      processed_response.strip! # Remove leading/trailing whitespace
      processed_response.presence || { error: 'AI returned an empty response after processing.' }
    else
      response.presence || { error: 'Failed to get response from Gemini or response was empty.' }
    end
  end
end
