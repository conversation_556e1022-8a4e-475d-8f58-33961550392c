# frozen_string_literal: true

class AiTextImprover
  def initialize(practice: Current.practice)
    @practice = practice
    @gemini_service = Ai::GeminiService.new(@practice)
  end

  def generate_title(message:)
    unless @gemini_service.send(:google_service_available?)
      return { error: 'Google AI service not available. Please upload a Google service account JSON file for this practice.' }
    end

    system_prompt = 'You are an AI assistant. You will be given a block of text. ' \
                    'Your task is to generate a concise and relevant title for this text, ideally between 3 to 7 words. ' \
                    'Return ONLY the generated title, with no extra text, ' \
                    'no introductory phrases like \'Here is a title:\', and no quotation marks around the title itself.'
    user_prompt = message

    messages = [
      { role: 'system', content: system_prompt },
      { role: 'user', content: user_prompt }
    ]

    response = @gemini_service.chat_completion(
      messages,
      temperature: 0.7,
      max_tokens: 30 # Specific for title generation
    )

    process_response(response)
  end

  def improve_text(message:, edits: 'sound more professional')
    unless @gemini_service.send(:google_service_available?)
      return { error: 'Google AI service not available. Please upload a Google service account JSON file for this practice.' }
    end

    system_prompt = "You will be given a user's message that they are writing and the edits that they would like to be made to it. " \
                    "Please only return the edited version with no extra text. You will be given the user's message first, " \
                    "then the edits they would like, separated by '---'."
    user_prompt = "#{message}\n---\nMake it #{edits}"

    messages = [
      { role: 'system', content: system_prompt },
      { role: 'user', content: user_prompt }
    ]

    response = @gemini_service.chat_completion(
      messages,
      temperature: 0.7,
      max_tokens: 256 # Specific for text improvement
    )

    process_response(response)
  end

  private

  def process_response(response)
    if response.is_a?(String)
      processed_response = response.gsub(/<[^>]*>/, '') # Remove HTML tags
      processed_response = processed_response.gsub(/^"|"$|^'|'$/, '') # Remove leading/trailing quotes
      processed_response.strip! # Remove leading/trailing whitespace
      processed_response.presence || { error: 'AI returned an empty response after processing.' }
    else
      response.presence || { error: 'Failed to get response from Gemini or response was empty.' }
    end
  end
end
