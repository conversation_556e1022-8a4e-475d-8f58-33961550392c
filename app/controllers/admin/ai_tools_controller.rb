# frozen_string_literal: true

module Admin
  class AiToolsController < Admin::ApplicationController
    def improve_text
      # Extract text from params - TinyMCE sends it as params[:text] directly
      # or nested in params[:ai_tool][:text]
      text = params[:text] || params.dig(:ai_tool, :text)
      task_type = params[:task_type] # No default here, let the service methods define defaults if needed

      # Try to get patient context from params or referer URL
      patient = find_patient_context

      ai_service = AiTextImprover.new(practice: Current.practice, patient: patient)

      result = if task_type == 'generate_title'
                 ai_service.generate_title(message: text)
               else # Default to improve_text or handle other task_types if they emerge
                 # For now, 'improve_text' is the only other defined task that uses 'edits'
                 # The 'edits' param is hardcoded here as per previous logic. Can be parameterized if needed.
                 ai_service.improve_text(message: text, edits: 'sound more professional')
               end

      if result.is_a?(Hash) && result[:error]
        render json: result, status: :bad_request
      else
        render json: { text: result }
      end
    end

    private

    def find_patient_context
      # Check if patient_id is provided in params
      return Patient.find(params[:patient_id]) if params[:patient_id].present?

      # Try to extract patient ID from referer URL
      referer = request.referer
      return nil unless referer

      # Match patterns like /admin/patients/123 or /admin/patients/123/something
      if referer.match(%r{/admin/patients/(\d+)})
        patient_id = Regexp.last_match(1)
        return Patient.find_by(id: patient_id)
      end

      nil
    rescue ActiveRecord::RecordNotFound
      nil
    end
  end
end
