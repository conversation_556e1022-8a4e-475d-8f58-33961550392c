# frozen_string_literal: true

module Admin
  module GeneralSettings
    class PracticesController < Admin::ApplicationController
      def index
        @practices = Practice.all
      end

      def new
        @practice = Practice.new
      end

      def edit
        @practice = Practice.find(params[:id])
      end

      def create
        @practice = Practice.new(practice_params)

        if @practice.save
          redirect_to admin_general_settings_practices_path, notice: 'Practice added!'
        else
          render :new
        end
      end

      def update
        @practice = Practice.find(params[:id])

        if @practice.update(practice_params)
          head :ok
        else
          render :edit
        end
      end

      def send_verification_code
        @practice = Practice.find(params[:id])
        sinch_service = Sinch::VerificationService.new(@practice)
        response = sinch_service.send_verification_code(current_user.mobile_phone)

        if response['id']
          render json: { success: true, verification_id: response['id'] }
        else
          render json: { success: false, error: response[:error] }
        end
      end

      def disable_two_factor
        @practice = Practice.find(params[:id])
        sinch_service = Sinch::VerificationService.new(@practice)
        response = sinch_service.report_verification_code(params[:verification_id], params[:code])
        success = response['status'] == 'SUCCESSFUL'

        if success
          browser = Browser.new(request.user_agent)
          disabled_data = {
            user_id: current_user.id,
            disabled_at: Time.current,
            sinch_verification_id: params[:verification_id],
            browser_data: {
              ip: request.remote_ip,
              user_agent: request.user_agent,
              device_type: browser.device.mobile? ? 'Mobile' : 'Desktop',
              os: browser.platform.name,
              os_version: browser.platform.version,
              browser: browser.name,
              browser_version: browser.version
            }
          }
          @practice.update(two_factor_enabled: false, two_factor_disabled_data: disabled_data)
        end

        render json: { success: success }
      end

      private

      def practice_params
        permitted_params = params.require(:practice).permit(
          :name,
          :website,
          :num_surgeries,
          :address_line_1,
          :address_line_2,
          :city,
          :postcode,
          :email,
          :phone,
          :primary_color,
          :secondary_color,
          :tertiary_color,
          :azure_url,
          :azure_token,
          :sinch_project_id,
          :sinch_app_id,
          :image,
          :favicon_image,
          :logo,
          :two_factor_enabled,
          :google_service_account_file,
          opening_hours: {}
        )

        # Handle Google service account JSON file upload
        if params[:practice][:google_service_account_file].present?
          file = params[:practice][:google_service_account_file]

          begin
            # Read and validate the JSON file
            json_content = file.read
            parsed_json = JSON.parse(json_content)

            # Validate required fields
            required_fields = %w[type project_id private_key client_email]
            missing_fields = required_fields.reject { |field| parsed_json[field].present? }

            if missing_fields.any?
              flash[:error] = "Invalid Google service account file. Missing required fields: #{missing_fields.join(', ')}"
              return permitted_params.except(:google_service_account_file)
            end

            # Validate that it's a service account
            unless parsed_json['type'] == 'service_account'
              flash[:error] = 'Invalid Google service account file. Must be a service account type.'
              return permitted_params.except(:google_service_account_file)
            end

            # Store the JSON content
            permitted_params[:google_service_account_json] = json_content
          rescue JSON::ParserError
            flash[:error] = 'Invalid JSON file. Please upload a valid Google service account JSON file.'
            return permitted_params.except(:google_service_account_file)
          rescue StandardError => e
            Rails.logger.error "Error processing Google service account file: #{e.message}"
            flash[:error] = 'Error processing the uploaded file. Please try again.'
            return permitted_params.except(:google_service_account_file)
          end
        end

        permitted_params.except(:google_service_account_file)
      end
    end
  end
end
